<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS FlexBox & GridBox 教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            overflow: hidden;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slideshow-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            display: none;
            width: 95vw;
            height: 90vh;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px;
            text-align: center;
            position: relative;
            animation: slideIn 0.8s ease-in-out;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        h1 {
            font-size: 4rem;
            color: #2c3e50;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        h2 {
            font-size: 3rem;
            color: #34495e;
            margin-bottom: 30px;
        }

        h3 {
            font-size: 2.5rem;
            color: #3498db;
            margin-bottom: 25px;
        }

        p, li {
            font-size: 1.8rem;
            line-height: 1.6;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        ul {
            text-align: left;
            max-width: 800px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 30px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 1.6rem;
            margin: 30px 0;
            text-align: left;
            overflow-x: auto;
        }

        .demo-container {
            background: #ecf0f1;
            border: 3px solid #3498db;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            min-height: 200px;
        }

        .flex-demo {
            display: flex;
            gap: 10px;
        }

        .flex-item {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 5px;
            font-size: 1.4rem;
            font-weight: bold;
        }

        .grid-demo {
            display: grid;
            gap: 10px;
        }

        .grid-item {
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 5px;
            font-size: 1.4rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
        }

        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(52, 73, 94, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1.2rem;
        }

        .highlight {
            background: linear-gradient(120deg, #f39c12 0%, #f1c40f 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
            margin: 0 5px;
        }

        .subtitle {
            font-size: 1.6rem;
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 40px;
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">65</span>
        </div>

        <!-- Slide 1: Title -->
        <div class="slide active">
            <h1>CSS FlexBox & GridBox</h1>
            <p class="subtitle">现代网页布局的核心技术</p>
            <p style="font-size: 2rem; margin-top: 60px;">📚 90分钟深度教程</p>
            <p style="font-size: 1.6rem; color: #7f8c8d;">适用于本科计算机专业学生</p>
        </div>

        <!-- Slide 2: Course Overview -->
        <div class="slide">
            <h2>课程概览</h2>
            <ul>
                <li>🎯 <strong>目标</strong>：掌握现代CSS布局技术</li>
                <li>⏱️ <strong>时长</strong>：90分钟</li>
                <li>📖 <strong>内容</strong>：FlexBox + GridBox 理论与实践</li>
                <li>🎓 <strong>适用</strong>：有HTML/CSS基础的学生</li>
                <li>💻 <strong>实践</strong>：大量代码示例和演示</li>
            </ul>
        </div>

        <!-- Slide 3: Learning Objectives -->
        <div class="slide">
            <h2>学习目标</h2>
            <ul>
                <li>理解FlexBox和GridBox的设计理念</li>
                <li>掌握FlexBox的核心属性和用法</li>
                <li>掌握GridBox的网格系统</li>
                <li>学会选择合适的布局方案</li>
                <li>能够解决常见的布局问题</li>
                <li>构建响应式网页布局</li>
            </ul>
        </div>

        <!-- Slide 4: Prerequisites -->
        <div class="slide">
            <h2>前置知识</h2>
            <ul>
                <li>✅ HTML基础语法</li>
                <li>✅ CSS基础选择器</li>
                <li>✅ CSS盒模型概念</li>
                <li>✅ 基本的CSS属性</li>
                <li>⚠️ 不需要深入的JavaScript知识</li>
                <li>💡 建议：有一定的网页开发经验</li>
            </ul>
        </div>

        <!-- Slide 5: Course Structure -->
        <div class="slide">
            <h2>课程结构</h2>
            <div style="display: flex; justify-content: space-around; align-items: center; height: 60%;">
                <div style="text-align: center;">
                    <h3 style="color: #3498db;">第一部分</h3>
                    <p>FlexBox 详解</p>
                    <p style="font-size: 1.4rem; color: #7f8c8d;">45分钟</p>
                </div>
                <div style="font-size: 4rem; color: #bdc3c7;">→</div>
                <div style="text-align: center;">
                    <h3 style="color: #e74c3c;">第二部分</h3>
                    <p>GridBox 详解</p>
                    <p style="font-size: 1.4rem; color: #7f8c8d;">45分钟</p>
                </div>
            </div>
        </div>

        <!-- Slide 6: Part 1 Title -->
        <div class="slide">
            <h1 style="color: #3498db;">第一部分</h1>
            <h2>CSS FlexBox</h2>
            <p class="subtitle">灵活的一维布局系统</p>
            <div style="font-size: 6rem; margin: 40px 0;">📦</div>
            <p style="font-size: 1.6rem; color: #7f8c8d;">让我们从基础开始...</p>
        </div>

        <!-- Slide 7: What is FlexBox -->
        <div class="slide">
            <h2>什么是 FlexBox？</h2>
            <ul>
                <li><span class="highlight">Flexible Box Layout</span> 的简称</li>
                <li>CSS3 引入的<strong>一维布局方法</strong></li>
                <li>专门用于在容器中<strong>分配空间</strong>和<strong>对齐项目</strong></li>
                <li>解决传统布局方案的痛点</li>
                <li>支持<strong>响应式设计</strong></li>
                <li>现代浏览器广泛支持</li>
            </ul>
        </div>

        <!-- Slide 8: FlexBox vs Traditional Layout -->
        <div class="slide">
            <h2>FlexBox vs 传统布局</h2>
            <div style="display: flex; justify-content: space-between; align-items: flex-start; height: 60%;">
                <div style="width: 45%; text-align: left;">
                    <h3 style="color: #e74c3c;">传统布局问题</h3>
                    <ul style="font-size: 1.6rem;">
                        <li>垂直居中困难</li>
                        <li>等高列布局复杂</li>
                        <li>空间分配不灵活</li>
                        <li>响应式适配麻烦</li>
                        <li>代码冗余</li>
                    </ul>
                </div>
                <div style="width: 45%; text-align: left;">
                    <h3 style="color: #27ae60;">FlexBox优势</h3>
                    <ul style="font-size: 1.6rem;">
                        <li>轻松实现居中</li>
                        <li>自动等高布局</li>
                        <li>灵活的空间分配</li>
                        <li>天然响应式</li>
                        <li>代码简洁</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 9: FlexBox Basic Concepts -->
        <div class="slide">
            <h2>FlexBox 基本概念</h2>
            <div style="display: flex; flex-direction: column; align-items: center; height: 70%;">
                <div style="background: #ecf0f1; border: 3px solid #3498db; border-radius: 10px; padding: 30px; width: 80%; margin-bottom: 30px;">
                    <h3 style="color: #3498db; margin-bottom: 20px;">Flex Container (容器)</h3>
                    <div style="background: #3498db; color: white; padding: 20px; border-radius: 5px; display: flex; justify-content: space-around;">
                        <div style="background: #e74c3c; padding: 15px; border-radius: 5px;">Item 1</div>
                        <div style="background: #e74c3c; padding: 15px; border-radius: 5px;">Item 2</div>
                        <div style="background: #e74c3c; padding: 15px; border-radius: 5px;">Item 3</div>
                    </div>
                    <p style="margin-top: 15px; font-size: 1.4rem; color: #7f8c8d;">Flex Items (项目)</p>
                </div>
                <p style="font-size: 1.6rem;"><strong>容器</strong>：设置 <code style="background: #f39c12; color: white; padding: 5px 10px; border-radius: 5px;">display: flex</code> 的元素</p>
                <p style="font-size: 1.6rem;"><strong>项目</strong>：容器的直接子元素</p>
            </div>
        </div>

        <!-- Slide 10: Flex Axes -->
        <div class="slide">
            <h2>FlexBox 坐标轴</h2>
            <div style="display: flex; flex-direction: column; align-items: center; height: 70%;">
                <div style="position: relative; width: 600px; height: 300px; background: #ecf0f1; border: 3px solid #3498db; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <!-- Main Axis Arrow -->
                    <div style="position: absolute; top: 50%; left: 10%; right: 10%; height: 3px; background: #e74c3c;"></div>
                    <div style="position: absolute; top: 50%; right: 8%; width: 0; height: 0; border-left: 15px solid #e74c3c; border-top: 8px solid transparent; border-bottom: 8px solid transparent;"></div>
                    <div style="position: absolute; top: 40%; right: 5%; color: #e74c3c; font-weight: bold; font-size: 1.4rem;">主轴 (Main Axis)</div>

                    <!-- Cross Axis Arrow -->
                    <div style="position: absolute; left: 50%; top: 10%; bottom: 10%; width: 3px; background: #27ae60;"></div>
                    <div style="position: absolute; left: 50%; top: 8%; width: 0; height: 0; border-bottom: 15px solid #27ae60; border-left: 8px solid transparent; border-right: 8px solid transparent;"></div>
                    <div style="position: absolute; left: 55%; top: 15%; color: #27ae60; font-weight: bold; font-size: 1.4rem;">交叉轴 (Cross Axis)</div>

                    <!-- Flex Items -->
                    <div style="background: #3498db; color: white; padding: 20px; margin: 10px; border-radius: 5px;">1</div>
                    <div style="background: #3498db; color: white; padding: 20px; margin: 10px; border-radius: 5px;">2</div>
                    <div style="background: #3498db; color: white; padding: 20px; margin: 10px; border-radius: 5px;">3</div>
                </div>
                <p style="margin-top: 30px; font-size: 1.6rem; text-align: center;">
                    <span style="color: #e74c3c;">主轴</span>：项目排列的方向 |
                    <span style="color: #27ae60;">交叉轴</span>：垂直于主轴的方向
                </p>
            </div>
        </div>

        <!-- Slide 11: Creating a Flex Container -->
        <div class="slide">
            <h2>创建 Flex 容器</h2>
            <div class="code-block">
.container {
    display: flex;        /* 创建flex容器 */
    /* 或者 */
    display: inline-flex; /* 创建行内flex容器 */
}</div>
            <div class="demo-container">
                <p style="margin-bottom: 20px; font-size: 1.4rem; color: #7f8c8d;">效果演示：</p>
                <div style="display: flex; background: #3498db; padding: 20px; border-radius: 5px;">
                    <div class="flex-item">项目 1</div>
                    <div class="flex-item">项目 2</div>
                    <div class="flex-item">项目 3</div>
                </div>
            </div>
            <p style="font-size: 1.6rem; margin-top: 20px;">✨ 子元素自动变成 flex items</p>
        </div>

        <!-- Slide 12: Flex Direction -->
        <div class="slide">
            <h2>flex-direction 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 30px;">决定主轴的方向（项目排列方向）</p>
            <div class="code-block">
.container {
    flex-direction: row;         /* 默认：水平从左到右 */
    flex-direction: row-reverse; /* 水平从右到左 */
    flex-direction: column;      /* 垂直从上到下 */
    flex-direction: column-reverse; /* 垂直从下到上 */
}</div>
            <div style="display: flex; justify-content: space-between; margin-top: 30px;">
                <div style="text-align: center;">
                    <p style="font-size: 1.4rem; margin-bottom: 10px;">row</p>
                    <div style="display: flex; background: #ecf0f1; padding: 15px; border-radius: 5px;">
                        <div style="background: #3498db; color: white; padding: 10px; margin: 2px; border-radius: 3px;">1</div>
                        <div style="background: #3498db; color: white; padding: 10px; margin: 2px; border-radius: 3px;">2</div>
                        <div style="background: #3498db; color: white; padding: 10px; margin: 2px; border-radius: 3px;">3</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <p style="font-size: 1.4rem; margin-bottom: 10px;">column</p>
                    <div style="display: flex; flex-direction: column; background: #ecf0f1; padding: 15px; border-radius: 5px;">
                        <div style="background: #e74c3c; color: white; padding: 10px; margin: 2px; border-radius: 3px;">1</div>
                        <div style="background: #e74c3c; color: white; padding: 10px; margin: 2px; border-radius: 3px;">2</div>
                        <div style="background: #e74c3c; color: white; padding: 10px; margin: 2px; border-radius: 3px;">3</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 13: Flex Wrap -->
        <div class="slide">
            <h2>flex-wrap 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 30px;">控制项目是否换行</p>
            <div class="code-block">
.container {
    flex-wrap: nowrap;   /* 默认：不换行 */
    flex-wrap: wrap;     /* 换行，第一行在上方 */
    flex-wrap: wrap-reverse; /* 换行，第一行在下方 */
}</div>
            <div style="display: flex; justify-content: space-between; margin-top: 30px;">
                <div style="width: 45%; text-align: center;">
                    <p style="font-size: 1.4rem; margin-bottom: 10px; color: #e74c3c;">nowrap (不换行)</p>
                    <div style="display: flex; flex-wrap: nowrap; background: #ecf0f1; padding: 15px; border-radius: 5px; overflow: hidden;">
                        <div style="background: #3498db; color: white; padding: 10px; margin: 2px; border-radius: 3px; min-width: 80px;">Item 1</div>
                        <div style="background: #3498db; color: white; padding: 10px; margin: 2px; border-radius: 3px; min-width: 80px;">Item 2</div>
                        <div style="background: #3498db; color: white; padding: 10px; margin: 2px; border-radius: 3px; min-width: 80px;">Item 3</div>
                        <div style="background: #3498db; color: white; padding: 10px; margin: 2px; border-radius: 3px; min-width: 80px;">Item 4</div>
                    </div>
                </div>
                <div style="width: 45%; text-align: center;">
                    <p style="font-size: 1.4rem; margin-bottom: 10px; color: #27ae60;">wrap (换行)</p>
                    <div style="display: flex; flex-wrap: wrap; background: #ecf0f1; padding: 15px; border-radius: 5px; width: 200px;">
                        <div style="background: #e74c3c; color: white; padding: 10px; margin: 2px; border-radius: 3px; min-width: 80px;">Item 1</div>
                        <div style="background: #e74c3c; color: white; padding: 10px; margin: 2px; border-radius: 3px; min-width: 80px;">Item 2</div>
                        <div style="background: #e74c3c; color: white; padding: 10px; margin: 2px; border-radius: 3px; min-width: 80px;">Item 3</div>
                        <div style="background: #e74c3c; color: white; padding: 10px; margin: 2px; border-radius: 3px; min-width: 80px;">Item 4</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 14: Flex Flow Shorthand -->
        <div class="slide">
            <h2>flex-flow 简写属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 30px;">flex-direction 和 flex-wrap 的简写</p>
            <div class="code-block">
.container {
    /* 分别设置 */
    flex-direction: row;
    flex-wrap: wrap;

    /* 简写形式 */
    flex-flow: row wrap;

    /* 其他常用组合 */
    flex-flow: column nowrap;
    flex-flow: row-reverse wrap;
}</div>
            <div style="background: #f8f9fa; border: 2px solid #dee2e6; border-radius: 10px; padding: 30px; margin-top: 30px;">
                <h3 style="color: #495057; margin-bottom: 20px;">💡 最佳实践</h3>
                <p style="font-size: 1.6rem; color: #6c757d;">推荐使用简写属性，代码更简洁易读</p>
            </div>
        </div>

        <!-- Slide 15: Justify Content -->
        <div class="slide">
            <h2>justify-content 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">控制项目在<span class="highlight">主轴</span>上的对齐方式</p>
            <div class="code-block" style="font-size: 1.4rem;">
justify-content: flex-start;    /* 起始端对齐（默认） */
justify-content: flex-end;      /* 末尾端对齐 */
justify-content: center;        /* 居中对齐 */
justify-content: space-between; /* 两端对齐 */
justify-content: space-around;  /* 环绕对齐 */
justify-content: space-evenly;  /* 平均分布 */</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px;">
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px;">flex-start</p>
                    <div style="display: flex; justify-content: flex-start; background: #ecf0f1; padding: 10px; border-radius: 5px;">
                        <div style="background: #3498db; color: white; padding: 8px; margin: 2px; border-radius: 3px;">1</div>
                        <div style="background: #3498db; color: white; padding: 8px; margin: 2px; border-radius: 3px;">2</div>
                        <div style="background: #3498db; color: white; padding: 8px; margin: 2px; border-radius: 3px;">3</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px;">center</p>
                    <div style="display: flex; justify-content: center; background: #ecf0f1; padding: 10px; border-radius: 5px;">
                        <div style="background: #e74c3c; color: white; padding: 8px; margin: 2px; border-radius: 3px;">1</div>
                        <div style="background: #e74c3c; color: white; padding: 8px; margin: 2px; border-radius: 3px;">2</div>
                        <div style="background: #e74c3c; color: white; padding: 8px; margin: 2px; border-radius: 3px;">3</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px;">space-between</p>
                    <div style="display: flex; justify-content: space-between; background: #ecf0f1; padding: 10px; border-radius: 5px;">
                        <div style="background: #f39c12; color: white; padding: 8px; margin: 2px; border-radius: 3px;">1</div>
                        <div style="background: #f39c12; color: white; padding: 8px; margin: 2px; border-radius: 3px;">2</div>
                        <div style="background: #f39c12; color: white; padding: 8px; margin: 2px; border-radius: 3px;">3</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px;">space-around</p>
                    <div style="display: flex; justify-content: space-around; background: #ecf0f1; padding: 10px; border-radius: 5px;">
                        <div style="background: #9b59b6; color: white; padding: 8px; margin: 2px; border-radius: 3px;">1</div>
                        <div style="background: #9b59b6; color: white; padding: 8px; margin: 2px; border-radius: 3px;">2</div>
                        <div style="background: #9b59b6; color: white; padding: 8px; margin: 2px; border-radius: 3px;">3</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 16: Align Items -->
        <div class="slide">
            <h2>align-items 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">控制项目在<span class="highlight">交叉轴</span>上的对齐方式</p>
            <div class="code-block" style="font-size: 1.4rem;">
align-items: stretch;    /* 拉伸填满（默认） */
align-items: flex-start; /* 起始端对齐 */
align-items: flex-end;   /* 末尾端对齐 */
align-items: center;     /* 居中对齐 */
align-items: baseline;   /* 基线对齐 */</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-top: 30px;">
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px;">flex-start</p>
                    <div style="display: flex; align-items: flex-start; background: #ecf0f1; padding: 10px; border-radius: 5px; height: 80px;">
                        <div style="background: #3498db; color: white; padding: 8px; margin: 2px; border-radius: 3px;">1</div>
                        <div style="background: #3498db; color: white; padding: 8px; margin: 2px; border-radius: 3px;">2</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px;">center</p>
                    <div style="display: flex; align-items: center; background: #ecf0f1; padding: 10px; border-radius: 5px; height: 80px;">
                        <div style="background: #e74c3c; color: white; padding: 8px; margin: 2px; border-radius: 3px;">1</div>
                        <div style="background: #e74c3c; color: white; padding: 8px; margin: 2px; border-radius: 3px;">2</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px;">stretch</p>
                    <div style="display: flex; align-items: stretch; background: #ecf0f1; padding: 10px; border-radius: 5px; height: 80px;">
                        <div style="background: #f39c12; color: white; padding: 8px; margin: 2px; border-radius: 3px; display: flex; align-items: center;">1</div>
                        <div style="background: #f39c12; color: white; padding: 8px; margin: 2px; border-radius: 3px; display: flex; align-items: center;">2</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 17: Align Content -->
        <div class="slide">
            <h2>align-content 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">控制<span class="highlight">多行</span>在交叉轴上的对齐方式</p>
            <div class="code-block" style="font-size: 1.4rem;">
align-content: stretch;       /* 拉伸填满（默认） */
align-content: flex-start;    /* 起始端对齐 */
align-content: flex-end;      /* 末尾端对齐 */
align-content: center;        /* 居中对齐 */
align-content: space-between; /* 两端对齐 */
align-content: space-around;  /* 环绕对齐 */</div>
            <div style="background: #fff3cd; border: 2px solid #ffeaa7; border-radius: 10px; padding: 20px; margin-top: 30px;">
                <p style="font-size: 1.6rem; color: #856404;">⚠️ <strong>注意</strong>：只有在多行（flex-wrap: wrap）时才有效果</p>
            </div>
        </div>

        <!-- Slide 18: Flex Item Properties Introduction -->
        <div class="slide">
            <h2>Flex 项目属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 40px;">控制单个项目的行为</p>
            <div style="display: flex; justify-content: space-around; align-items: center; height: 60%;">
                <div style="text-align: center; background: #e8f4fd; padding: 30px; border-radius: 15px; width: 200px;">
                    <h3 style="color: #3498db; margin-bottom: 20px;">flex-grow</h3>
                    <p style="font-size: 1.4rem;">放大比例</p>
                </div>
                <div style="text-align: center; background: #fdf2e8; padding: 30px; border-radius: 15px; width: 200px;">
                    <h3 style="color: #f39c12; margin-bottom: 20px;">flex-shrink</h3>
                    <p style="font-size: 1.4rem;">缩小比例</p>
                </div>
                <div style="text-align: center; background: #e8f8f5; padding: 30px; border-radius: 15px; width: 200px;">
                    <h3 style="color: #27ae60; margin-bottom: 20px;">flex-basis</h3>
                    <p style="font-size: 1.4rem;">基础大小</p>
                </div>
            </div>
            <p style="font-size: 1.6rem; margin-top: 40px; text-align: center;">
                还有 <span class="highlight">align-self</span> 用于单独对齐
            </p>
        </div>

        <!-- Slide 19: Flex Grow -->
        <div class="slide">
            <h2>flex-grow 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">定义项目的<span class="highlight">放大比例</span></p>
            <div class="code-block">
.item {
    flex-grow: 0; /* 默认值，不放大 */
    flex-grow: 1; /* 等比例放大 */
    flex-grow: 2; /* 放大比例为其他项目的2倍 */
}</div>
            <div style="margin-top: 30px;">
                <p style="font-size: 1.4rem; margin-bottom: 15px; color: #7f8c8d;">示例：三个项目，中间项目 flex-grow: 2</p>
                <div style="display: flex; background: #ecf0f1; padding: 15px; border-radius: 5px; height: 80px;">
                    <div style="background: #3498db; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-grow: 1; display: flex; align-items: center; justify-content: center;">
                        flex-grow: 1
                    </div>
                    <div style="background: #e74c3c; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-grow: 2; display: flex; align-items: center; justify-content: center;">
                        flex-grow: 2
                    </div>
                    <div style="background: #3498db; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-grow: 1; display: flex; align-items: center; justify-content: center;">
                        flex-grow: 1
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 20: Flex Shrink -->
        <div class="slide">
            <h2>flex-shrink 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">定义项目的<span class="highlight">缩小比例</span></p>
            <div class="code-block">
.item {
    flex-shrink: 1; /* 默认值，等比例缩小 */
    flex-shrink: 0; /* 不缩小 */
    flex-shrink: 2; /* 缩小比例为其他项目的2倍 */
}</div>
            <div style="margin-top: 30px;">
                <p style="font-size: 1.4rem; margin-bottom: 15px; color: #7f8c8d;">示例：容器宽度不足时，第二个项目不缩小</p>
                <div style="display: flex; background: #ecf0f1; padding: 15px; border-radius: 5px; width: 400px; overflow: hidden;">
                    <div style="background: #3498db; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-shrink: 1; min-width: 120px; display: flex; align-items: center; justify-content: center; font-size: 1.2rem;">
                        shrink: 1
                    </div>
                    <div style="background: #e74c3c; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-shrink: 0; min-width: 120px; display: flex; align-items: center; justify-content: center; font-size: 1.2rem;">
                        shrink: 0
                    </div>
                    <div style="background: #3498db; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-shrink: 1; min-width: 120px; display: flex; align-items: center; justify-content: center; font-size: 1.2rem;">
                        shrink: 1
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 21: Flex Basis -->
        <div class="slide">
            <h2>flex-basis 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">定义项目的<span class="highlight">基础大小</span></p>
            <div class="code-block">
.item {
    flex-basis: auto;  /* 默认值，基于内容大小 */
    flex-basis: 200px; /* 固定基础宽度 */
    flex-basis: 30%;   /* 百分比基础宽度 */
    flex-basis: 0;     /* 基础大小为0 */
}</div>
            <div style="margin-top: 30px;">
                <p style="font-size: 1.4rem; margin-bottom: 15px; color: #7f8c8d;">示例：不同的 flex-basis 值</p>
                <div style="display: flex; background: #ecf0f1; padding: 15px; border-radius: 5px;">
                    <div style="background: #3498db; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-basis: 100px; display: flex; align-items: center; justify-content: center; font-size: 1.2rem;">
                        basis: 100px
                    </div>
                    <div style="background: #e74c3c; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-basis: 200px; display: flex; align-items: center; justify-content: center; font-size: 1.2rem;">
                        basis: 200px
                    </div>
                    <div style="background: #f39c12; color: white; padding: 15px; margin: 5px; border-radius: 5px; flex-basis: auto; display: flex; align-items: center; justify-content: center; font-size: 1.2rem;">
                        basis: auto
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 22: Flex Shorthand -->
        <div class="slide">
            <h2>flex 简写属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">flex-grow、flex-shrink、flex-basis 的简写</p>
            <div class="code-block">
.item {
    /* flex: grow shrink basis */
    flex: 1 1 auto;    /* 默认值 */
    flex: 1;           /* flex: 1 1 0 */
    flex: auto;        /* flex: 1 1 auto */
    flex: none;        /* flex: 0 0 auto */
    flex: 0 1 200px;   /* 自定义组合 */
}</div>
            <div style="background: #e8f5e8; border: 2px solid #27ae60; border-radius: 10px; padding: 30px; margin-top: 30px;">
                <h3 style="color: #27ae60; margin-bottom: 20px;">💡 常用简写值</h3>
                <ul style="font-size: 1.6rem; color: #2d5a2d;">
                    <li><strong>flex: 1</strong> - 平均分配剩余空间</li>
                    <li><strong>flex: auto</strong> - 基于内容大小，可伸缩</li>
                    <li><strong>flex: none</strong> - 固定大小，不伸缩</li>
                </ul>
            </div>
        </div>

        <!-- Slide 23: Align Self -->
        <div class="slide">
            <h2>align-self 属性</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">单独控制某个项目的<span class="highlight">交叉轴对齐</span></p>
            <div class="code-block">
.item {
    align-self: auto;       /* 继承容器的align-items */
    align-self: flex-start; /* 起始端对齐 */
    align-self: flex-end;   /* 末尾端对齐 */
    align-self: center;     /* 居中对齐 */
    align-self: baseline;   /* 基线对齐 */
    align-self: stretch;    /* 拉伸填满 */
}</div>
            <div style="margin-top: 30px;">
                <p style="font-size: 1.4rem; margin-bottom: 15px; color: #7f8c8d;">示例：中间项目使用不同的 align-self</p>
                <div style="display: flex; align-items: flex-start; background: #ecf0f1; padding: 15px; border-radius: 5px; height: 120px;">
                    <div style="background: #3498db; color: white; padding: 15px; margin: 5px; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                        Normal
                    </div>
                    <div style="background: #e74c3c; color: white; padding: 15px; margin: 5px; border-radius: 5px; align-self: center; display: flex; align-items: center; justify-content: center;">
                        align-self: center
                    </div>
                    <div style="background: #3498db; color: white; padding: 15px; margin: 5px; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                        Normal
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 24: FlexBox Common Patterns -->
        <div class="slide">
            <h2>FlexBox 常见布局模式</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 40px;">
                <div>
                    <h3 style="color: #3498db; margin-bottom: 15px;">1. 水平居中</h3>
                    <div class="code-block" style="font-size: 1.2rem; margin-bottom: 15px;">
.container {
    display: flex;
    justify-content: center;
}</div>
                    <div style="display: flex; justify-content: center; background: #ecf0f1; padding: 15px; border-radius: 5px;">
                        <div style="background: #3498db; color: white; padding: 10px; border-radius: 3px;">居中项目</div>
                    </div>
                </div>
                <div>
                    <h3 style="color: #e74c3c; margin-bottom: 15px;">2. 垂直居中</h3>
                    <div class="code-block" style="font-size: 1.2rem; margin-bottom: 15px;">
.container {
    display: flex;
    align-items: center;
}</div>
                    <div style="display: flex; align-items: center; background: #ecf0f1; padding: 15px; border-radius: 5px; height: 80px;">
                        <div style="background: #e74c3c; color: white; padding: 10px; border-radius: 3px;">居中项目</div>
                    </div>
                </div>
                <div>
                    <h3 style="color: #f39c12; margin-bottom: 15px;">3. 完全居中</h3>
                    <div class="code-block" style="font-size: 1.2rem; margin-bottom: 15px;">
.container {
    display: flex;
    justify-content: center;
    align-items: center;
}</div>
                    <div style="display: flex; justify-content: center; align-items: center; background: #ecf0f1; padding: 15px; border-radius: 5px; height: 80px;">
                        <div style="background: #f39c12; color: white; padding: 10px; border-radius: 3px;">完全居中</div>
                    </div>
                </div>
                <div>
                    <h3 style="color: #9b59b6; margin-bottom: 15px;">4. 等高列</h3>
                    <div class="code-block" style="font-size: 1.2rem; margin-bottom: 15px;">
.container {
    display: flex;
    align-items: stretch;
}</div>
                    <div style="display: flex; align-items: stretch; background: #ecf0f1; padding: 15px; border-radius: 5px; height: 80px;">
                        <div style="background: #9b59b6; color: white; padding: 10px; margin: 2px; border-radius: 3px; flex: 1; display: flex; align-items: center; justify-content: center;">列1</div>
                        <div style="background: #9b59b6; color: white; padding: 10px; margin: 2px; border-radius: 3px; flex: 1; display: flex; align-items: center; justify-content: center;">列2</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 25: FlexBox Navigation Bar -->
        <div class="slide">
            <h2>实战案例：导航栏</h2>
            <div class="code-block" style="font-size: 1.3rem;">
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: #2c3e50;
}

.nav-links {
    display: flex;
    gap: 2rem;
}</div>
            <div style="margin-top: 30px;">
                <p style="font-size: 1.4rem; margin-bottom: 15px; color: #7f8c8d;">效果演示：</p>
                <div style="display: flex; justify-content: space-between; align-items: center; background: #2c3e50; color: white; padding: 20px; border-radius: 5px;">
                    <div style="font-size: 1.6rem; font-weight: bold;">LOGO</div>
                    <div style="display: flex; gap: 30px;">
                        <span>首页</span>
                        <span>产品</span>
                        <span>关于</span>
                        <span>联系</span>
                    </div>
                    <div style="background: #3498db; padding: 8px 16px; border-radius: 4px;">登录</div>
                </div>
            </div>
        </div>

        <!-- Slide 26: FlexBox Card Layout -->
        <div class="slide">
            <h2>实战案例：卡片布局</h2>
            <div class="code-block" style="font-size: 1.3rem;">
.card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.card {
    flex: 1 1 300px; /* 最小宽度300px，可伸缩 */
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
}</div>
            <div style="margin-top: 30px;">
                <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                    <div style="flex: 1 1 200px; background: white; border: 2px solid #ecf0f1; border-radius: 8px; padding: 20px; min-height: 120px;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">卡片 1</h4>
                        <p style="font-size: 1.3rem; color: #7f8c8d;">这是一个响应式卡片</p>
                    </div>
                    <div style="flex: 1 1 200px; background: white; border: 2px solid #ecf0f1; border-radius: 8px; padding: 20px; min-height: 120px;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">卡片 2</h4>
                        <p style="font-size: 1.3rem; color: #7f8c8d;">自动调整大小</p>
                    </div>
                    <div style="flex: 1 1 200px; background: white; border: 2px solid #ecf0f1; border-radius: 8px; padding: 20px; min-height: 120px;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">卡片 3</h4>
                        <p style="font-size: 1.3rem; color: #7f8c8d;">灵活布局</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 27: FlexBox Holy Grail Layout -->
        <div class="slide">
            <h2>实战案例：圣杯布局</h2>
            <div class="code-block" style="font-size: 1.2rem;">
.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main {
    display: flex;
    flex: 1;
}

.sidebar { flex: 0 0 200px; }
.content { flex: 1; }
.footer { flex: 0 0 auto; }</div>
            <div style="margin-top: 20px;">
                <div style="display: flex; flex-direction: column; background: #ecf0f1; border-radius: 5px; height: 300px;">
                    <div style="background: #34495e; color: white; padding: 15px; text-align: center;">Header</div>
                    <div style="display: flex; flex: 1;">
                        <div style="background: #3498db; color: white; padding: 15px; width: 120px; display: flex; align-items: center; justify-content: center;">Sidebar</div>
                        <div style="background: #ecf0f1; flex: 1; padding: 15px; display: flex; align-items: center; justify-content: center; color: #2c3e50;">Main Content</div>
                        <div style="background: #e74c3c; color: white; padding: 15px; width: 120px; display: flex; align-items: center; justify-content: center;">Aside</div>
                    </div>
                    <div style="background: #34495e; color: white; padding: 15px; text-align: center;">Footer</div>
                </div>
            </div>
        </div>

        <!-- Slide 28: FlexBox Best Practices -->
        <div class="slide">
            <h2>FlexBox 最佳实践</h2>
            <ul style="font-size: 1.6rem;">
                <li>✅ <strong>优先使用简写属性</strong>：flex、flex-flow</li>
                <li>✅ <strong>明确主轴方向</strong>：设置 flex-direction</li>
                <li>✅ <strong>合理使用 gap</strong>：替代 margin 设置间距</li>
                <li>✅ <strong>避免固定高度</strong>：让内容决定高度</li>
                <li>✅ <strong>响应式设计</strong>：结合媒体查询</li>
                <li>⚠️ <strong>注意浏览器兼容性</strong>：IE11 部分支持</li>
                <li>⚠️ <strong>避免过度嵌套</strong>：保持结构清晰</li>
            </ul>
        </div>

        <!-- Slide 29: FlexBox vs Grid When to Use -->
        <div class="slide">
            <h2>FlexBox 适用场景</h2>
            <div style="display: flex; justify-content: space-between; margin-top: 40px;">
                <div style="width: 45%; background: #e8f5e8; padding: 30px; border-radius: 10px;">
                    <h3 style="color: #27ae60; margin-bottom: 20px;">✅ 适合使用 FlexBox</h3>
                    <ul style="font-size: 1.4rem; color: #2d5a2d;">
                        <li>导航栏布局</li>
                        <li>按钮组排列</li>
                        <li>卡片容器</li>
                        <li>垂直/水平居中</li>
                        <li>一维布局</li>
                        <li>内容驱动的布局</li>
                    </ul>
                </div>
                <div style="width: 45%; background: #fdf2e8; padding: 30px; border-radius: 10px;">
                    <h3 style="color: #f39c12; margin-bottom: 20px;">⚠️ 考虑其他方案</h3>
                    <ul style="font-size: 1.4rem; color: #8b4513;">
                        <li>复杂的二维布局</li>
                        <li>网格系统</li>
                        <li>表格式数据展示</li>
                        <li>精确的位置控制</li>
                        <li>大型页面布局</li>
                        <li>需要重叠元素</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 30: Part 2 Title -->
        <div class="slide">
            <h1 style="color: #e74c3c;">第二部分</h1>
            <h2>CSS Grid Layout</h2>
            <p class="subtitle">强大的二维布局系统</p>
            <div style="font-size: 6rem; margin: 40px 0;">🔲</div>
            <p style="font-size: 1.6rem; color: #7f8c8d;">从一维到二维的飞跃...</p>
        </div>

        <!-- Slide 31: What is CSS Grid -->
        <div class="slide">
            <h2>什么是 CSS Grid？</h2>
            <ul>
                <li><span class="highlight">二维布局系统</span>：同时控制行和列</li>
                <li>CSS3 最强大的布局方案</li>
                <li>专为<strong>复杂布局</strong>而设计</li>
                <li>网格容器 + 网格项目</li>
                <li>精确的位置控制</li>
                <li>现代浏览器全面支持</li>
            </ul>
        </div>

        <!-- Slide 32: Grid vs FlexBox -->
        <div class="slide">
            <h2>Grid vs FlexBox</h2>
            <div style="display: flex; justify-content: space-between; align-items: flex-start; height: 70%;">
                <div style="width: 45%; text-align: left;">
                    <h3 style="color: #3498db; margin-bottom: 20px;">FlexBox (一维)</h3>
                    <div style="background: #e8f4fd; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <div style="display: flex; gap: 10px;">
                            <div style="background: #3498db; color: white; padding: 15px; border-radius: 5px; flex: 1; text-align: center;">1</div>
                            <div style="background: #3498db; color: white; padding: 15px; border-radius: 5px; flex: 1; text-align: center;">2</div>
                            <div style="background: #3498db; color: white; padding: 15px; border-radius: 5px; flex: 1; text-align: center;">3</div>
                        </div>
                    </div>
                    <ul style="font-size: 1.4rem;">
                        <li>内容驱动</li>
                        <li>一个方向布局</li>
                        <li>灵活的空间分配</li>
                    </ul>
                </div>
                <div style="width: 45%; text-align: left;">
                    <h3 style="color: #e74c3c; margin-bottom: 20px;">Grid (二维)</h3>
                    <div style="background: #fdf2e8; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; grid-template-rows: 1fr 1fr; gap: 10px;">
                            <div style="background: #e74c3c; color: white; padding: 15px; border-radius: 5px; text-align: center;">1</div>
                            <div style="background: #e74c3c; color: white; padding: 15px; border-radius: 5px; text-align: center;">2</div>
                            <div style="background: #e74c3c; color: white; padding: 15px; border-radius: 5px; text-align: center;">3</div>
                            <div style="background: #e74c3c; color: white; padding: 15px; border-radius: 5px; text-align: center;">4</div>
                            <div style="background: #e74c3c; color: white; padding: 15px; border-radius: 5px; text-align: center;">5</div>
                            <div style="background: #e74c3c; color: white; padding: 15px; border-radius: 5px; text-align: center;">6</div>
                        </div>
                    </div>
                    <ul style="font-size: 1.4rem;">
                        <li>布局驱动</li>
                        <li>二维网格系统</li>
                        <li>精确的位置控制</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 33: Grid Basic Concepts -->
        <div class="slide">
            <h2>Grid 基本概念</h2>
            <div style="display: flex; flex-direction: column; align-items: center; height: 70%;">
                <div style="position: relative; width: 500px; height: 300px; background: #ecf0f1; border: 3px solid #e74c3c; border-radius: 10px; padding: 20px;">
                    <!-- Grid Lines -->
                    <div style="position: absolute; top: 20px; bottom: 20px; left: 20px; width: 2px; background: #34495e;"></div>
                    <div style="position: absolute; top: 20px; bottom: 20px; left: 170px; width: 2px; background: #34495e;"></div>
                    <div style="position: absolute; top: 20px; bottom: 20px; left: 320px; width: 2px; background: #34495e;"></div>
                    <div style="position: absolute; top: 20px; bottom: 20px; right: 20px; width: 2px; background: #34495e;"></div>

                    <div style="position: absolute; left: 20px; right: 20px; top: 20px; height: 2px; background: #34495e;"></div>
                    <div style="position: absolute; left: 20px; right: 20px; top: 120px; height: 2px; background: #34495e;"></div>
                    <div style="position: absolute; left: 20px; right: 20px; top: 220px; height: 2px; background: #34495e;"></div>
                    <div style="position: absolute; left: 20px; right: 20px; bottom: 20px; height: 2px; background: #34495e;"></div>

                    <!-- Grid Items -->
                    <div style="position: absolute; top: 30px; left: 30px; width: 130px; height: 80px; background: #e74c3c; color: white; display: flex; align-items: center; justify-content: center; border-radius: 5px;">Item 1</div>
                    <div style="position: absolute; top: 30px; left: 180px; width: 130px; height: 80px; background: #e74c3c; color: white; display: flex; align-items: center; justify-content: center; border-radius: 5px;">Item 2</div>
                    <div style="position: absolute; top: 130px; left: 30px; width: 130px; height: 80px; background: #e74c3c; color: white; display: flex; align-items: center; justify-content: center; border-radius: 5px;">Item 3</div>

                    <!-- Labels -->
                    <div style="position: absolute; top: -15px; left: 30px; font-size: 1.2rem; color: #7f8c8d;">网格线</div>
                    <div style="position: absolute; bottom: -40px; left: 30px; font-size: 1.2rem; color: #7f8c8d;">网格轨道</div>
                    <div style="position: absolute; top: 50px; right: -80px; font-size: 1.2rem; color: #7f8c8d;">网格单元</div>
                </div>
                <div style="margin-top: 40px; display: flex; justify-content: space-around; width: 100%;">
                    <p style="font-size: 1.4rem;"><strong>网格线</strong>：分割网格的线</p>
                    <p style="font-size: 1.4rem;"><strong>网格轨道</strong>：行或列</p>
                    <p style="font-size: 1.4rem;"><strong>网格单元</strong>：最小单位</p>
                </div>
            </div>
        </div>

        <!-- Slide 34: Creating a Grid Container -->
        <div class="slide">
            <h2>创建 Grid 容器</h2>
            <div class="code-block">
.container {
    display: grid;        /* 创建块级网格容器 */
    /* 或者 */
    display: inline-grid; /* 创建行内网格容器 */
}</div>
            <div class="demo-container">
                <p style="margin-bottom: 20px; font-size: 1.4rem; color: #7f8c8d;">效果演示：</p>
                <div style="display: grid; background: #e74c3c; padding: 20px; border-radius: 5px;">
                    <div class="grid-item">项目 1</div>
                    <div class="grid-item">项目 2</div>
                    <div class="grid-item">项目 3</div>
                </div>
            </div>
            <p style="font-size: 1.6rem; margin-top: 20px;">✨ 默认情况下，所有项目堆叠在一列中</p>
        </div>

        <!-- Slide 35: Grid Template Columns -->
        <div class="slide">
            <h2>grid-template-columns</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">定义<span class="highlight">列的大小</span>和数量</p>
            <div class="code-block">
.container {
    grid-template-columns: 100px 200px 100px; /* 固定宽度 */
    grid-template-columns: 1fr 2fr 1fr;       /* 比例分配 */
    grid-template-columns: repeat(3, 1fr);    /* 重复模式 */
    grid-template-columns: auto 1fr auto;     /* 混合使用 */
}</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px;">
                <div>
                    <p style="font-size: 1.3rem; margin-bottom: 10px; color: #7f8c8d;">固定宽度：100px 200px 100px</p>
                    <div style="display: grid; grid-template-columns: 100px 200px 100px; gap: 10px; background: #ecf0f1; padding: 15px; border-radius: 5px;">
                        <div class="grid-item">1</div>
                        <div class="grid-item">2</div>
                        <div class="grid-item">3</div>
                    </div>
                </div>
                <div>
                    <p style="font-size: 1.3rem; margin-bottom: 10px; color: #7f8c8d;">比例分配：1fr 2fr 1fr</p>
                    <div style="display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 10px; background: #ecf0f1; padding: 15px; border-radius: 5px;">
                        <div class="grid-item">1</div>
                        <div class="grid-item">2</div>
                        <div class="grid-item">3</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 36: Grid Template Rows -->
        <div class="slide">
            <h2>grid-template-rows</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">定义<span class="highlight">行的大小</span>和数量</p>
            <div class="code-block">
.container {
    grid-template-rows: 100px 200px;      /* 固定高度 */
    grid-template-rows: 1fr 2fr;          /* 比例分配 */
    grid-template-rows: repeat(3, 100px); /* 重复模式 */
    grid-template-rows: auto 1fr auto;    /* 混合使用 */
}</div>
            <div style="display: flex; justify-content: space-around; margin-top: 30px;">
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px; color: #7f8c8d;">固定高度</p>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; grid-template-rows: 60px 40px; gap: 5px; background: #ecf0f1; padding: 15px; border-radius: 5px; width: 200px;">
                        <div class="grid-item" style="font-size: 1.2rem;">1</div>
                        <div class="grid-item" style="font-size: 1.2rem;">2</div>
                        <div class="grid-item" style="font-size: 1.2rem;">3</div>
                        <div class="grid-item" style="font-size: 1.2rem;">4</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <p style="font-size: 1.3rem; margin-bottom: 10px; color: #7f8c8d;">比例分配</p>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; grid-template-rows: 1fr 2fr; gap: 5px; background: #ecf0f1; padding: 15px; border-radius: 5px; width: 200px; height: 150px;">
                        <div class="grid-item" style="font-size: 1.2rem;">1</div>
                        <div class="grid-item" style="font-size: 1.2rem;">2</div>
                        <div class="grid-item" style="font-size: 1.2rem;">3</div>
                        <div class="grid-item" style="font-size: 1.2rem;">4</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 37: FR Unit -->
        <div class="slide">
            <h2>fr 单位详解</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;"><span class="highlight">fr</span> = fraction（分数单位）</p>
            <div class="code-block">
/* 1fr = 1个分数单位 */
grid-template-columns: 1fr 1fr 1fr;    /* 三等分 */
grid-template-columns: 1fr 2fr;        /* 1:2 比例 */
grid-template-columns: 100px 1fr 50px; /* 固定+弹性+固定 */</div>
            <div style="margin-top: 30px;">
                <h3 style="color: #e74c3c; margin-bottom: 15px;">计算方式</h3>
                <div style="background: #fdf2e8; border: 2px solid #f39c12; border-radius: 10px; padding: 20px;">
                    <p style="font-size: 1.6rem; margin-bottom: 10px;">容器宽度：600px</p>
                    <p style="font-size: 1.6rem; margin-bottom: 10px;">设置：<code style="background: #f39c12; color: white; padding: 3px 8px; border-radius: 3px;">100px 1fr 2fr 50px</code></p>
                    <p style="font-size: 1.6rem; margin-bottom: 10px;">剩余空间：600 - 100 - 50 = 450px</p>
                    <p style="font-size: 1.6rem; margin-bottom: 10px;">总分数：1fr + 2fr = 3fr</p>
                    <p style="font-size: 1.6rem;">结果：100px + 150px + 300px + 50px</p>
                </div>
            </div>
        </div>

        <!-- Slide 38: Repeat Function -->
        <div class="slide">
            <h2>repeat() 函数</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">简化<span class="highlight">重复模式</span>的写法</p>
            <div class="code-block">
/* 基本语法 */
repeat(次数, 模式)

/* 示例 */
grid-template-columns: repeat(3, 1fr);
/* 等同于：1fr 1fr 1fr */

grid-template-columns: repeat(2, 100px 200px);
/* 等同于：100px 200px 100px 200px */

grid-template-columns: 50px repeat(3, 1fr) 50px;
/* 等同于：50px 1fr 1fr 1fr 50px */</div>
            <div style="margin-top: 30px;">
                <p style="font-size: 1.4rem; margin-bottom: 15px; color: #7f8c8d;">示例：repeat(4, 1fr)</p>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; background: #ecf0f1; padding: 15px; border-radius: 5px;">
                    <div class="grid-item">1</div>
                    <div class="grid-item">2</div>
                    <div class="grid-item">3</div>
                    <div class="grid-item">4</div>
                </div>
            </div>
        </div>

        <!-- Slide 39: Auto-fit and Auto-fill -->
        <div class="slide">
            <h2>auto-fit 和 auto-fill</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">响应式网格的关键</p>
            <div class="code-block">
/* auto-fill：创建尽可能多的轨道 */
grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));

/* auto-fit：轨道适应内容 */
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));</div>
            <div style="display: flex; justify-content: space-between; margin-top: 30px;">
                <div style="width: 45%;">
                    <h3 style="color: #3498db; margin-bottom: 15px;">auto-fill</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); gap: 5px; background: #ecf0f1; padding: 15px; border-radius: 5px; width: 300px;">
                        <div class="grid-item" style="font-size: 1.2rem;">1</div>
                        <div class="grid-item" style="font-size: 1.2rem;">2</div>
                        <div class="grid-item" style="font-size: 1.2rem;">3</div>
                    </div>
                    <p style="font-size: 1.3rem; color: #7f8c8d; margin-top: 10px;">保持空轨道</p>
                </div>
                <div style="width: 45%;">
                    <h3 style="color: #e74c3c; margin-bottom: 15px;">auto-fit</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(80px, 1fr)); gap: 5px; background: #ecf0f1; padding: 15px; border-radius: 5px; width: 300px;">
                        <div class="grid-item" style="font-size: 1.2rem;">1</div>
                        <div class="grid-item" style="font-size: 1.2rem;">2</div>
                        <div class="grid-item" style="font-size: 1.2rem;">3</div>
                    </div>
                    <p style="font-size: 1.3rem; color: #7f8c8d; margin-top: 10px;">折叠空轨道</p>
                </div>
            </div>
        </div>

        <!-- Slide 40: Minmax Function -->
        <div class="slide">
            <h2>minmax() 函数</h2>
            <p style="font-size: 1.8rem; margin-bottom: 20px;">设置轨道的<span class="highlight">最小值和最大值</span></p>
            <div class="code-block">
/* 基本语法 */
minmax(最小值, 最大值)

/* 示例 */
grid-template-columns: minmax(200px, 1fr);
/* 最小200px，最大占满剩余空间 */

grid-template-columns: repeat(3, minmax(100px, 300px));
/* 每列最小100px，最大300px */

grid-template-columns: minmax(0, 1fr) 200px minmax(100px, 1fr);
/* 混合使用 */</div>
            <div style="margin-top: 30px;">
                <p style="font-size: 1.4rem; margin-bottom: 15px; color: #7f8c8d;">示例：minmax(100px, 200px)</p>
                <div style="display: grid; grid-template-columns: repeat(3, minmax(100px, 200px)); gap: 10px; background: #ecf0f1; padding: 15px; border-radius: 5px; justify-content: center;">
                    <div class="grid-item">最小100px</div>
                    <div class="grid-item">最大200px</div>
                    <div class="grid-item">自适应</div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prev-btn" onclick="changeSlide(-1)">← 上一页</button>
        <button class="nav-btn" id="next-btn" onclick="changeSlide(1)">下一页 →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            
            // Update navigation buttons
            document.getElementById('prev-btn').disabled = currentSlide === 0;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            if (direction === 1 && currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            } else if (direction === -1 && currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                changeSlide(1);
            } else if (e.key === 'ArrowLeft') {
                changeSlide(-1);
            }
        });

        // Initialize
        showSlide(0);
    </script>
</body>
</html>

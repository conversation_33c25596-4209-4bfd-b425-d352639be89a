<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apache Spark 概述 - SVG 动画幻灯片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            overflow: hidden;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .slideshow-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .slide {
            display: none;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
        }
        
        .slide.active {
            display: block;
            animation: slideIn 0.8s ease-in-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .controls {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            color: #333;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            z-index: 1000;
        }
        
        svg {
            width: 100%;
            height: 100%;
        }
        
        .slide-title {
            font-size: 48px;
            font-weight: bold;
            fill: #2c3e50;
            text-anchor: middle;
        }
        
        .slide-subtitle {
            font-size: 32px;
            fill: #34495e;
            text-anchor: middle;
        }
        
        .slide-text {
            font-size: 24px;
            fill: #2c3e50;
            text-anchor: start;
        }
        
        .slide-text-center {
            font-size: 24px;
            fill: #2c3e50;
            text-anchor: middle;
        }
        
        .highlight {
            fill: #e74c3c;
            font-weight: bold;
        }
        
        .accent {
            fill: #3498db;
            font-weight: bold;
        }
        
        .animate-text {
            animation: fadeInUp 1s ease-out forwards;
            opacity: 0;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-delay-1 { animation-delay: 0.2s; }
        .animate-delay-2 { animation-delay: 0.4s; }
        .animate-delay-3 { animation-delay: 0.6s; }
        .animate-delay-4 { animation-delay: 0.8s; }
        .animate-delay-5 { animation-delay: 1.0s; }
        .animate-delay-6 { animation-delay: 1.2s; }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">12</span>
        </div>
        
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <svg viewBox="0 0 1920 1080">
                <defs>
                    <linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#bg1)"/>
                
                <text x="960" y="400" class="slide-title animate-text">Apache Spark</text>
                <text x="960" y="480" class="slide-subtitle animate-text animate-delay-1">大数据处理引擎概述</text>
                
                <circle cx="960" cy="600" r="80" fill="#e74c3c" opacity="0.8" class="animate-text animate-delay-2">
                    <animate attributeName="r" values="80;90;80" dur="2s" repeatCount="indefinite"/>
                </circle>
                <text x="960" y="615" class="slide-text-center animate-text animate-delay-2" fill="white" font-weight="bold">SPARK</text>
                
                <text x="960" y="750" class="slide-text-center animate-text animate-delay-3">快速 • 通用 • 易用</text>
                <text x="960" y="800" class="slide-text-center animate-text animate-delay-4">统一的大数据处理平台</text>
            </svg>
        </div>

        <!-- 第2页：什么是 Spark -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="150" class="slide-title animate-text">什么是 Apache Spark？</text>

                <rect x="200" y="250" width="1520" height="600" fill="rgba(255,255,255,0.8)" rx="20" class="animate-text animate-delay-1"/>

                <text x="960" y="320" class="slide-text-center animate-text animate-delay-2">
                    Apache Spark 是一个<tspan class="highlight">统一的分析引擎</tspan>，用于大规模数据处理
                </text>

                <text x="300" y="400" class="slide-text animate-text animate-delay-3">• 开源的分布式计算系统</text>
                <text x="300" y="450" class="slide-text animate-text animate-delay-4">• 支持批处理、流处理、机器学习和图计算</text>
                <text x="300" y="500" class="slide-text animate-text animate-delay-5">• 比 Hadoop MapReduce 快 100 倍（内存中）</text>
                <text x="300" y="550" class="slide-text animate-text animate-delay-6">• 提供 Java、Scala、Python、R 和 SQL API</text>

                <circle cx="1400" cy="500" r="120" fill="#3498db" opacity="0.3" class="animate-text animate-delay-2">
                    <animate attributeName="opacity" values="0.3;0.6;0.3" dur="3s" repeatCount="indefinite"/>
                </circle>
                <text x="1400" y="510" class="slide-text-center animate-text animate-delay-3" fill="#2c3e50" font-weight="bold">快速</text>

                <rect x="300" y="650" width="1320" height="120" fill="rgba(52, 152, 219, 0.1)" rx="10" class="animate-text animate-delay-4"/>
                <text x="960" y="700" class="slide-text-center animate-text animate-delay-5" font-size="28px">
                    <tspan class="accent">核心优势：</tspan>内存计算 + 优化的执行引擎 + 统一的编程模型
                </text>
            </svg>
        </div>

        <!-- 第3页：Spark 核心组件 -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">Spark 核心组件</text>

                <!-- Spark Core -->
                <rect x="760" y="200" width="400" height="120" fill="#e74c3c" rx="15" class="animate-text animate-delay-1"/>
                <text x="960" y="270" class="slide-text-center animate-text animate-delay-1" fill="white" font-weight="bold">Spark Core</text>
                <text x="960" y="300" class="slide-text-center animate-text animate-delay-1" fill="white" font-size="18px">基础执行引擎</text>

                <!-- 四个组件围绕 Core -->
                <rect x="200" y="400" width="300" height="100" fill="#3498db" rx="10" class="animate-text animate-delay-2"/>
                <text x="350" y="460" class="slide-text-center animate-text animate-delay-2" fill="white" font-weight="bold">Spark SQL</text>
                <text x="350" y="485" class="slide-text-center animate-text animate-delay-2" fill="white" font-size="16px">结构化数据</text>

                <rect x="1420" y="400" width="300" height="100" fill="#9b59b6" rx="10" class="animate-text animate-delay-3"/>
                <text x="1570" y="460" class="slide-text-center animate-text animate-delay-3" fill="white" font-weight="bold">Spark Streaming</text>
                <text x="1570" y="485" class="slide-text-center animate-text animate-delay-3" fill="white" font-size="16px">实时流处理</text>

                <rect x="200" y="600" width="300" height="100" fill="#f39c12" rx="10" class="animate-text animate-delay-4"/>
                <text x="350" y="660" class="slide-text-center animate-text animate-delay-4" fill="white" font-weight="bold">MLlib</text>
                <text x="350" y="685" class="slide-text-center animate-delay-4" fill="white" font-size="16px">机器学习库</text>

                <rect x="1420" y="600" width="300" height="100" fill="#27ae60" rx="10" class="animate-text animate-delay-5"/>
                <text x="1570" y="660" class="slide-text-center animate-text animate-delay-5" fill="white" font-weight="bold">GraphX</text>
                <text x="1570" y="685" class="slide-text-center animate-text animate-delay-5" fill="white" font-size="16px">图计算</text>

                <!-- 连接线 -->
                <line x1="760" y1="260" x2="500" y2="450" stroke="#666" stroke-width="3" class="animate-text animate-delay-2"/>
                <line x1="1160" y1="260" x2="1420" y2="450" stroke="#666" stroke-width="3" class="animate-text animate-delay-3"/>
                <line x1="760" y1="260" x2="500" y2="650" stroke="#666" stroke-width="3" class="animate-text animate-delay-4"/>
                <line x1="1160" y1="260" x2="1420" y2="650" stroke="#666" stroke-width="3" class="animate-text animate-delay-5"/>

                <text x="960" y="850" class="slide-text-center animate-text animate-delay-6">
                    所有组件共享相同的<tspan class="highlight">RDD</tspan>抽象和执行引擎
                </text>
            </svg>
        </div>

        <!-- 第4页：RDD 概念 -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">RDD - 弹性分布式数据集</text>
                <text x="960" y="180" class="slide-subtitle animate-text animate-delay-1">Resilient Distributed Dataset</text>

                <rect x="150" y="250" width="1620" height="650" fill="rgba(255,255,255,0.9)" rx="20" class="animate-text animate-delay-1"/>

                <text x="300" y="320" class="slide-text animate-text animate-delay-2" font-size="28px" class="accent">核心特性：</text>

                <circle cx="250" cy="400" r="8" fill="#e74c3c" class="animate-text animate-delay-3"/>
                <text x="280" y="410" class="slide-text animate-text animate-delay-3">
                    <tspan class="highlight">弹性（Resilient）：</tspan>自动容错，数据丢失时可重新计算
                </text>

                <circle cx="250" cy="460" r="8" fill="#e74c3c" class="animate-text animate-delay-4"/>
                <text x="280" y="470" class="slide-text animate-text animate-delay-4">
                    <tspan class="highlight">分布式（Distributed）：</tspan>数据分布在集群的多个节点上
                </text>

                <circle cx="250" cy="520" r="8" fill="#e74c3c" class="animate-text animate-delay-5"/>
                <text x="280" y="530" class="slide-text animate-text animate-delay-5">
                    <tspan class="highlight">数据集（Dataset）：</tspan>不可变的数据集合
                </text>

                <text x="300" y="620" class="slide-text animate-text animate-delay-6" font-size="28px" class="accent">操作类型：</text>

                <rect x="300" y="660" width="600" height="80" fill="#3498db" opacity="0.2" rx="10" class="animate-text animate-delay-7"/>
                <text x="600" y="690" class="slide-text-center animate-text animate-delay-7" font-weight="bold">转换操作 (Transformations)</text>
                <text x="600" y="720" class="slide-text-center animate-text animate-delay-7">map, filter, groupBy, join...</text>

                <rect x="1020" y="660" width="600" height="80" fill="#27ae60" opacity="0.2" rx="10" class="animate-text animate-delay-8"/>
                <text x="1320" y="690" class="slide-text-center animate-text animate-delay-8" font-weight="bold">行动操作 (Actions)</text>
                <text x="1320" y="720" class="slide-text-center animate-text animate-delay-8">collect, count, save, reduce...</text>

                <text x="960" y="820" class="slide-text-center animate-text animate-delay-9" font-size="20px">
                    转换操作是<tspan class="highlight">惰性的</tspan>，只有遇到行动操作才会真正执行
                </text>
            </svg>
        </div>

        <!-- 第5页：Spark 架构 -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">Spark 集群架构</text>

                <!-- Driver Program -->
                <rect x="100" y="200" width="300" height="150" fill="#e74c3c" rx="15" class="animate-text animate-delay-1"/>
                <text x="250" y="260" class="slide-text-center animate-text animate-delay-1" fill="white" font-weight="bold">Driver Program</text>
                <text x="250" y="290" class="slide-text-center animate-text animate-delay-1" fill="white" font-size="18px">SparkContext</text>
                <text x="250" y="320" class="slide-text-center animate-text animate-delay-1" fill="white" font-size="16px">任务调度</text>

                <!-- Cluster Manager -->
                <rect x="810" y="200" width="300" height="150" fill="#3498db" rx="15" class="animate-text animate-delay-2"/>
                <text x="960" y="260" class="slide-text-center animate-text animate-delay-2" fill="white" font-weight="bold">Cluster Manager</text>
                <text x="960" y="290" class="slide-text-center animate-text animate-delay-2" fill="white" font-size="18px">资源管理</text>
                <text x="960" y="320" class="slide-text-center animate-text animate-delay-2" fill="white" font-size="16px">YARN/Mesos/K8s</text>

                <!-- Worker Nodes -->
                <rect x="200" y="500" width="250" height="200" fill="#27ae60" rx="15" class="animate-text animate-delay-3"/>
                <text x="325" y="540" class="slide-text-center animate-text animate-delay-3" fill="white" font-weight="bold">Worker Node 1</text>
                <rect x="220" y="570" width="100" height="60" fill="rgba(255,255,255,0.3)" rx="5"/>
                <text x="270" y="595" class="slide-text-center" fill="white" font-size="14px">Executor</text>
                <text x="270" y="615" class="slide-text-center" fill="white" font-size="14px">Tasks</text>
                <rect x="330" y="570" width="100" height="60" fill="rgba(255,255,255,0.3)" rx="5"/>
                <text x="380" y="595" class="slide-text-center" fill="white" font-size="14px">Executor</text>
                <text x="380" y="615" class="slide-text-center" fill="white" font-size="14px">Tasks</text>

                <rect x="600" y="500" width="250" height="200" fill="#27ae60" rx="15" class="animate-text animate-delay-4"/>
                <text x="725" y="540" class="slide-text-center animate-text animate-delay-4" fill="white" font-weight="bold">Worker Node 2</text>
                <rect x="620" y="570" width="100" height="60" fill="rgba(255,255,255,0.3)" rx="5"/>
                <text x="670" y="595" class="slide-text-center" fill="white" font-size="14px">Executor</text>
                <text x="670" y="615" class="slide-text-center" fill="white" font-size="14px">Tasks</text>
                <rect x="730" y="570" width="100" height="60" fill="rgba(255,255,255,0.3)" rx="5"/>
                <text x="780" y="595" class="slide-text-center" fill="white" font-size="14px">Executor</text>
                <text x="780" y="615" class="slide-text-center" fill="white" font-size="14px">Tasks</text>

                <rect x="1000" y="500" width="250" height="200" fill="#27ae60" rx="15" class="animate-text animate-delay-5"/>
                <text x="1125" y="540" class="slide-text-center animate-text animate-delay-5" fill="white" font-weight="bold">Worker Node 3</text>
                <rect x="1020" y="570" width="100" height="60" fill="rgba(255,255,255,0.3)" rx="5"/>
                <text x="1070" y="595" class="slide-text-center" fill="white" font-size="14px">Executor</text>
                <text x="1070" y="615" class="slide-text-center" fill="white" font-size="14px">Tasks</text>
                <rect x="1130" y="570" width="100" height="60" fill="rgba(255,255,255,0.3)" rx="5"/>
                <text x="1180" y="595" class="slide-text-center" fill="white" font-size="14px">Executor</text>
                <text x="1180" y="615" class="slide-text-center" fill="white" font-size="14px">Tasks</text>

                <!-- 连接线 -->
                <line x1="400" y1="275" x2="810" y2="275" stroke="#666" stroke-width="3" class="animate-text animate-delay-2"/>
                <line x1="960" y1="350" x2="325" y2="500" stroke="#666" stroke-width="3" class="animate-text animate-delay-3"/>
                <line x1="960" y1="350" x2="725" y2="500" stroke="#666" stroke-width="3" class="animate-text animate-delay-4"/>
                <line x1="960" y1="350" x2="1125" y2="500" stroke="#666" stroke-width="3" class="animate-text animate-delay-5"/>

                <text x="960" y="800" class="slide-text-center animate-text animate-delay-6">
                    Driver 负责任务调度，Executor 负责任务执行
                </text>
            </svg>
        </div>

        <!-- 第6页：Spark SQL -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">Spark SQL</text>
                <text x="960" y="180" class="slide-subtitle animate-text animate-delay-1">结构化数据处理</text>

                <rect x="150" y="250" width="1620" height="650" fill="rgba(255,255,255,0.9)" rx="20" class="animate-text animate-delay-1"/>

                <text x="300" y="320" class="slide-text animate-text animate-delay-2" font-size="28px" class="accent">核心功能：</text>

                <text x="300" y="380" class="slide-text animate-text animate-delay-3">• 支持标准 SQL 查询语法</text>
                <text x="300" y="430" class="slide-text animate-text animate-delay-4">• DataFrame 和 Dataset API</text>
                <text x="300" y="480" class="slide-text animate-text animate-delay-5">• 多种数据源支持（JSON、Parquet、Hive、JDBC）</text>
                <text x="300" y="530" class="slide-text animate-text animate-delay-6">• Catalyst 优化器自动优化查询</text>

                <rect x="1000" y="350" width="600" height="300" fill="rgba(52, 152, 219, 0.1)" rx="15" class="animate-text animate-delay-3"/>
                <text x="1300" y="390" class="slide-text-center animate-text animate-delay-4" font-size="20px" font-weight="bold">代码示例</text>

                <text x="1050" y="440" class="slide-text animate-text animate-delay-5" font-family="monospace" font-size="18px">
                    // 创建 DataFrame
                </text>
                <text x="1050" y="470" class="slide-text animate-text animate-delay-6" font-family="monospace" font-size="18px">
                    val df = spark.read.json("data.json")
                </text>
                <text x="1050" y="520" class="slide-text animate-text animate-delay-7" font-family="monospace" font-size="18px">
                    // SQL 查询
                </text>
                <text x="1050" y="550" class="slide-text animate-text animate-delay-8" font-family="monospace" font-size="18px">
                    df.createOrReplaceTempView("people")
                </text>
                <text x="1050" y="580" class="slide-text animate-text animate-delay-9" font-family="monospace" font-size="18px">
                    spark.sql("SELECT * FROM people")
                </text>

                <text x="960" y="750" class="slide-text-center animate-text animate-delay-10" font-size="24px">
                    <tspan class="highlight">统一接口：</tspan>SQL、DataFrame API 和 RDD 可以无缝集成
                </text>

                <text x="960" y="820" class="slide-text-center animate-text animate-delay-11" font-size="20px">
                    性能优化：Catalyst 优化器 + Tungsten 执行引擎
                </text>
            </svg>
        </div>

        <!-- 第7页：Spark Streaming -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">Spark Streaming</text>
                <text x="960" y="180" class="slide-subtitle animate-text animate-delay-1">实时流数据处理</text>

                <!-- 数据流示意图 -->
                <rect x="100" y="250" width="200" height="80" fill="#3498db" rx="10" class="animate-text animate-delay-2"/>
                <text x="200" y="300" class="slide-text-center animate-text animate-delay-2" fill="white" font-weight="bold">数据源</text>

                <rect x="400" y="250" width="200" height="80" fill="#e74c3c" rx="10" class="animate-text animate-delay-3"/>
                <text x="500" y="290" class="slide-text-center animate-text animate-delay-3" fill="white" font-weight="bold">Spark</text>
                <text x="500" y="310" class="slide-text-center animate-text animate-delay-3" fill="white" font-weight="bold">Streaming</text>

                <rect x="700" y="250" width="200" height="80" fill="#27ae60" rx="10" class="animate-text animate-delay-4"/>
                <text x="800" y="300" class="slide-text-center animate-text animate-delay-4" fill="white" font-weight="bold">输出</text>

                <!-- 箭头 -->
                <polygon points="320,290 380,290 370,280 380,290 370,300" fill="#666" class="animate-text animate-delay-3"/>
                <polygon points="620,290 680,290 670,280 680,290 670,300" fill="#666" class="animate-text animate-delay-4"/>

                <text x="200" y="380" class="slide-text-center animate-text animate-delay-5">Kafka, Flume</text>
                <text x="200" y="400" class="slide-text-center animate-text animate-delay-5">TCP Socket</text>
                <text x="800" y="380" class="slide-text-center animate-text animate-delay-6">HDFS, Database</text>
                <text x="800" y="400" class="slide-text-center animate-text animate-delay-6">Dashboard</text>

                <rect x="200" y="500" width="1520" height="400" fill="rgba(255,255,255,0.9)" rx="20" class="animate-text animate-delay-2"/>

                <text x="300" y="560" class="slide-text animate-text animate-delay-7" font-size="28px" class="accent">核心特性：</text>

                <text x="300" y="620" class="slide-text animate-text animate-delay-8">• 微批处理模型（Micro-batch）</text>
                <text x="300" y="670" class="slide-text animate-delay-9">• 容错性：自动恢复失败的批次</text>
                <text x="300" y="720" class="slide-text animate-text animate-delay-10">• 与 Spark Core 完全集成</text>
                <text x="300" y="770" class="slide-text animate-text animate-delay-11">• 支持窗口操作和状态管理</text>

                <text x="1000" y="560" class="slide-text animate-text animate-delay-7" font-size="28px" class="accent">处理模式：</text>

                <circle cx="1050" cy="620" r="8" fill="#e74c3c" class="animate-text animate-delay-8"/>
                <text x="1080" y="630" class="slide-text animate-text animate-delay-8">DStream（离散流）</text>

                <circle cx="1050" cy="670" r="8" fill="#e74c3c" class="animate-text animate-delay-9"/>
                <text x="1080" y="680" class="slide-text animate-text animate-delay-9">Structured Streaming</text>

                <text x="960" y="850" class="slide-text-center animate-text animate-delay-12" font-size="20px">
                    <tspan class="highlight">实时性：</tspan>秒级延迟，准实时数据处理
                </text>
            </svg>
        </div>

        <!-- 第8页：MLlib 机器学习 -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">MLlib - 机器学习库</text>
                <text x="960" y="180" class="slide-subtitle animate-text animate-delay-1">可扩展的机器学习算法</text>

                <rect x="150" y="250" width="1620" height="650" fill="rgba(255,255,255,0.9)" rx="20" class="animate-text animate-delay-1"/>

                <!-- 算法分类 -->
                <rect x="250" y="320" width="350" height="200" fill="#3498db" opacity="0.2" rx="15" class="animate-text animate-delay-2"/>
                <text x="425" y="360" class="slide-text-center animate-text animate-delay-2" font-size="24px" font-weight="bold">分类算法</text>
                <text x="280" y="400" class="slide-text animate-text animate-delay-3">• 逻辑回归</text>
                <text x="280" y="430" class="slide-text animate-text animate-delay-3">• 决策树</text>
                <text x="280" y="460" class="slide-text animate-text animate-delay-3">• 随机森林</text>
                <text x="280" y="490" class="slide-text animate-text animate-delay-3">• 支持向量机</text>

                <rect x="650" y="320" width="350" height="200" fill="#e74c3c" opacity="0.2" rx="15" class="animate-text animate-delay-4"/>
                <text x="825" y="360" class="slide-text-center animate-text animate-delay-4" font-size="24px" font-weight="bold">聚类算法</text>
                <text x="680" y="400" class="slide-text animate-text animate-delay-5">• K-means</text>
                <text x="680" y="430" class="slide-text animate-text animate-delay-5">• 高斯混合模型</text>
                <text x="680" y="460" class="slide-text animate-text animate-delay-5">• LDA</text>
                <text x="680" y="490" class="slide-text animate-text animate-delay-5">• 层次聚类</text>

                <rect x="1050" y="320" width="350" height="200" fill="#27ae60" opacity="0.2" rx="15" class="animate-text animate-delay-6"/>
                <text x="1225" y="360" class="slide-text-center animate-text animate-delay-6" font-size="24px" font-weight="bold">回归算法</text>
                <text x="1080" y="400" class="slide-text animate-text animate-delay-7">• 线性回归</text>
                <text x="1080" y="430" class="slide-text animate-text animate-delay-7">• 岭回归</text>
                <text x="1080" y="460" class="slide-text animate-text animate-delay-7">• Lasso 回归</text>
                <text x="1080" y="490" class="slide-text animate-text animate-delay-7">• 弹性网络</text>

                <rect x="450" y="580" width="350" height="200" fill="#9b59b6" opacity="0.2" rx="15" class="animate-text animate-delay-8"/>
                <text x="625" y="620" class="slide-text-center animate-text animate-delay-8" font-size="24px" font-weight="bold">推荐系统</text>
                <text x="480" y="660" class="slide-text animate-text animate-delay-9">• 协同过滤</text>
                <text x="480" y="690" class="slide-text animate-text animate-delay-9">• 矩阵分解</text>
                <text x="480" y="720" class="slide-text animate-text animate-delay-9">• ALS 算法</text>

                <rect x="850" y="580" width="350" height="200" fill="#f39c12" opacity="0.2" rx="15" class="animate-text animate-delay-10"/>
                <text x="1025" y="620" class="slide-text-center animate-text animate-delay-10" font-size="24px" font-weight="bold">特征工程</text>
                <text x="880" y="660" class="slide-text animate-text animate-delay-11">• 特征提取</text>
                <text x="880" y="690" class="slide-text animate-text animate-delay-11">• 特征转换</text>
                <text x="880" y="720" class="slide-text animate-text animate-delay-11">• 特征选择</text>

                <text x="960" y="850" class="slide-text-center animate-text animate-delay-12" font-size="20px">
                    <tspan class="highlight">分布式训练：</tspan>支持大规模数据集的机器学习模型训练
                </text>
            </svg>
        </div>

        <!-- 第9页：GraphX 图计算 -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">GraphX - 图计算</text>
                <text x="960" y="180" class="slide-subtitle animate-text animate-delay-1">大规模图数据处理</text>

                <rect x="150" y="250" width="1620" height="650" fill="rgba(255,255,255,0.9)" rx="20" class="animate-text animate-delay-1"/>

                <!-- 图示例 -->
                <g class="animate-text animate-delay-2">
                    <circle cx="400" cy="400" r="30" fill="#3498db"/>
                    <text x="400" y="410" class="slide-text-center" fill="white" font-weight="bold">A</text>

                    <circle cx="600" cy="350" r="30" fill="#3498db"/>
                    <text x="600" y="360" class="slide-text-center" fill="white" font-weight="bold">B</text>

                    <circle cx="600" cy="450" r="30" fill="#3498db"/>
                    <text x="600" y="460" class="slide-text-center" fill="white" font-weight="bold">C</text>

                    <circle cx="800" cy="400" r="30" fill="#3498db"/>
                    <text x="800" y="410" class="slide-text-center" fill="white" font-weight="bold">D</text>

                    <line x1="430" y1="400" x2="570" y2="350" stroke="#e74c3c" stroke-width="3"/>
                    <line x1="430" y1="400" x2="570" y2="450" stroke="#e74c3c" stroke-width="3"/>
                    <line x1="630" y1="350" x2="770" y2="400" stroke="#e74c3c" stroke-width="3"/>
                    <line x1="630" y1="450" x2="770" y2="400" stroke="#e74c3c" stroke-width="3"/>
                    <line x1="600" y1="380" x2="600" y2="420" stroke="#e74c3c" stroke-width="3"/>
                </g>

                <text x="500" y="550" class="slide-text-center animate-text animate-delay-3" font-size="20px">图数据结构示例</text>

                <text x="1000" y="320" class="slide-text animate-text animate-delay-4" font-size="28px" class="accent">核心功能：</text>

                <text x="1000" y="380" class="slide-text animate-text animate-delay-5">• 图的创建和操作</text>
                <text x="1000" y="420" class="slide-text animate-text animate-delay-6">• 图算法库（PageRank、连通分量）</text>
                <text x="1000" y="460" class="slide-text animate-text animate-delay-7">• 图的转换和聚合</text>
                <text x="1000" y="500" class="slide-text animate-text animate-delay-8">• 与 Spark SQL 集成</text>

                <text x="300" y="650" class="slide-text animate-text animate-delay-9" font-size="28px" class="accent">应用场景：</text>

                <circle cx="250" cy="700" r="8" fill="#e74c3c" class="animate-text animate-delay-10"/>
                <text x="280" y="710" class="slide-text animate-text animate-delay-10">社交网络分析</text>

                <circle cx="250" cy="740" r="8" fill="#e74c3c" class="animate-text animate-delay-11"/>
                <text x="280" y="750" class="slide-text animate-text animate-delay-11">推荐系统</text>

                <circle cx="500" cy="700" r="8" fill="#e74c3c" class="animate-text animate-delay-10"/>
                <text x="530" y="710" class="slide-text animate-text animate-delay-10">网络安全</text>

                <circle cx="500" cy="740" r="8" fill="#e74c3c" class="animate-text animate-delay-11"/>
                <text x="530" y="750" class="slide-text animate-text animate-delay-11">生物信息学</text>

                <text x="960" y="850" class="slide-text-center animate-text animate-delay-12" font-size="20px">
                    <tspan class="highlight">统一模型：</tspan>图和表格数据的统一处理
                </text>
            </svg>
        </div>

        <!-- 第10页：Spark 优势 -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">Spark 的核心优势</text>

                <rect x="150" y="200" width="1620" height="700" fill="rgba(255,255,255,0.9)" rx="20" class="animate-text animate-delay-1"/>

                <!-- 四个优势 -->
                <rect x="250" y="300" width="350" height="250" fill="#3498db" opacity="0.1" rx="15" class="animate-text animate-delay-2"/>
                <circle cx="425" cy="360" r="40" fill="#3498db" class="animate-text animate-delay-2">
                    <animate attributeName="r" values="40;45;40" dur="2s" repeatCount="indefinite"/>
                </circle>
                <text x="425" y="370" class="slide-text-center animate-text animate-delay-2" fill="white" font-weight="bold" font-size="20px">快</text>
                <text x="425" y="440" class="slide-text-center animate-text animate-delay-3" font-size="24px" font-weight="bold">高性能</text>
                <text x="280" y="480" class="slide-text animate-text animate-delay-3">• 内存计算</text>
                <text x="280" y="510" class="slide-text animate-text animate-delay-3">• DAG 执行引擎</text>
                <text x="280" y="540" class="slide-text animate-text animate-delay-3">• 代码生成优化</text>

                <rect x="650" y="300" width="350" height="250" fill="#e74c3c" opacity="0.1" rx="15" class="animate-text animate-delay-4"/>
                <circle cx="825" cy="360" r="40" fill="#e74c3c" class="animate-text animate-delay-4">
                    <animate attributeName="r" values="40;45;40" dur="2s" repeatCount="indefinite"/>
                </circle>
                <text x="825" y="370" class="slide-text-center animate-text animate-delay-4" fill="white" font-weight="bold" font-size="20px">易</text>
                <text x="825" y="440" class="slide-text-center animate-text animate-delay-5" font-size="24px" font-weight="bold">易用性</text>
                <text x="680" y="480" class="slide-text animate-text animate-delay-5">• 多语言 API</text>
                <text x="680" y="510" class="slide-text animate-text animate-delay-5">• 交互式 Shell</text>
                <text x="680" y="540" class="slide-text animate-text animate-delay-5">• 丰富的算子</text>

                <rect x="1050" y="300" width="350" height="250" fill="#27ae60" opacity="0.1" rx="15" class="animate-text animate-delay-6"/>
                <circle cx="1225" cy="360" r="40" fill="#27ae60" class="animate-text animate-delay-6">
                    <animate attributeName="r" values="40;45;40" dur="2s" repeatCount="indefinite"/>
                </circle>
                <text x="1225" y="370" class="slide-text-center animate-text animate-delay-6" fill="white" font-weight="bold" font-size="20px">通</text>
                <text x="1225" y="440" class="slide-text-center animate-text animate-delay-7" font-size="24px" font-weight="bold">通用性</text>
                <text x="1080" y="480" class="slide-text animate-text animate-delay-7">• 批处理</text>
                <text x="1080" y="510" class="slide-text animate-text animate-delay-7">• 流处理</text>
                <text x="1080" y="540" class="slide-text animate-text animate-delay-7">• 机器学习</text>

                <rect x="450" y="600" width="350" height="250" fill="#9b59b6" opacity="0.1" rx="15" class="animate-text animate-delay-8"/>
                <circle cx="625" cy="660" r="40" fill="#9b59b6" class="animate-text animate-delay-8">
                    <animate attributeName="r" values="40;45;40" dur="2s" repeatCount="indefinite"/>
                </circle>
                <text x="625" y="670" class="slide-text-center animate-text animate-delay-8" fill="white" font-weight="bold" font-size="20px">容</text>
                <text x="625" y="740" class="slide-text-center animate-text animate-delay-9" font-size="24px" font-weight="bold">容错性</text>
                <text x="480" y="780" class="slide-text animate-text animate-delay-9">• 自动故障恢复</text>
                <text x="480" y="810" class="slide-text animate-text animate-delay-9">• 数据血缘追踪</text>
                <text x="480" y="840" class="slide-text animate-text animate-delay-9">• 检查点机制</text>

                <rect x="850" y="600" width="350" height="250" fill="#f39c12" opacity="0.1" rx="15" class="animate-text animate-delay-10"/>
                <circle cx="1025" cy="660" r="40" fill="#f39c12" class="animate-text animate-delay-10">
                    <animate attributeName="r" values="40;45;40" dur="2s" repeatCount="indefinite"/>
                </circle>
                <text x="1025" y="670" class="slide-text-center animate-text animate-delay-10" fill="white" font-weight="bold" font-size="20px">扩</text>
                <text x="1025" y="740" class="slide-text-center animate-text animate-delay-11" font-size="24px" font-weight="bold">可扩展</text>
                <text x="880" y="780" class="slide-text animate-text animate-delay-11">• 水平扩展</text>
                <text x="880" y="810" class="slide-text animate-text animate-delay-11">• 动态资源分配</text>
                <text x="880" y="840" class="slide-text animate-text animate-delay-11">• 多集群支持</text>
            </svg>
        </div>

        <!-- 第11页：应用场景 -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="120" class="slide-title animate-text">Spark 应用场景</text>

                <rect x="150" y="200" width="1620" height="700" fill="rgba(255,255,255,0.9)" rx="20" class="animate-text animate-delay-1"/>

                <text x="300" y="280" class="slide-text animate-text animate-delay-2" font-size="32px" class="accent">典型应用场景</text>

                <!-- 场景列表 -->
                <rect x="250" y="350" width="700" height="500" fill="rgba(52, 152, 219, 0.05)" rx="15" class="animate-text animate-delay-2"/>

                <circle cx="300" cy="400" r="12" fill="#3498db" class="animate-text animate-delay-3"/>
                <text x="330" y="410" class="slide-text animate-text animate-delay-3" font-size="24px" font-weight="bold">大数据 ETL 处理</text>
                <text x="330" y="440" class="slide-text animate-text animate-delay-3">数据清洗、转换、加载</text>

                <circle cx="300" cy="490" r="12" fill="#e74c3c" class="animate-text animate-delay-4"/>
                <text x="330" y="500" class="slide-text animate-text animate-delay-4" font-size="24px" font-weight="bold">实时数据分析</text>
                <text x="330" y="530" class="slide-text animate-text animate-delay-4">流数据处理、实时监控</text>

                <circle cx="300" cy="580" r="12" fill="#27ae60" class="animate-text animate-delay-5"/>
                <text x="330" y="590" class="slide-text animate-text animate-delay-5" font-size="24px" font-weight="bold">机器学习平台</text>
                <text x="330" y="620" class="slide-text animate-text animate-delay-5">模型训练、特征工程</text>

                <circle cx="300" cy="670" r="12" fill="#9b59b6" class="animate-text animate-delay-6"/>
                <text x="330" y="680" class="slide-text animate-text animate-delay-6" font-size="24px" font-weight="bold">图数据分析</text>
                <text x="330" y="710" class="slide-text animate-text animate-delay-6">社交网络、推荐系统</text>

                <circle cx="300" cy="760" r="12" fill="#f39c12" class="animate-text animate-delay-7"/>
                <text x="330" y="770" class="slide-text animate-text animate-delay-7" font-size="24px" font-weight="bold">交互式数据探索</text>
                <text x="330" y="800" class="slide-text animate-text animate-delay-7">即席查询、数据挖掘</text>

                <!-- 行业应用 -->
                <rect x="1000" y="350" width="650" height="500" fill="rgba(231, 76, 60, 0.05)" rx="15" class="animate-text animate-delay-3"/>

                <text x="1050" y="400" class="slide-text animate-text animate-delay-8" font-size="28px" class="highlight">行业应用</text>

                <text x="1050" y="460" class="slide-text animate-text animate-delay-9" font-size="22px">🏦 金融：风险控制、欺诈检测</text>
                <text x="1050" y="510" class="slide-text animate-text animate-delay-10" font-size="22px">🛒 电商：推荐系统、用户画像</text>
                <text x="1050" y="560" class="slide-text animate-text animate-delay-11" font-size="22px">📱 互联网：日志分析、A/B测试</text>
                <text x="1050" y="610" class="slide-text animate-text animate-delay-12" font-size="22px">🏭 制造业：设备监控、质量分析</text>
                <text x="1050" y="660" class="slide-text animate-text animate-delay-13" font-size="22px">🏥 医疗：基因分析、药物研发</text>
                <text x="1050" y="710" class="slide-text animate-text animate-delay-14" font-size="22px">🚗 交通：路径优化、智能调度</text>
                <text x="1050" y="760" class="slide-text animate-text animate-delay-15" font-size="22px">📺 媒体：内容推荐、用户分析</text>
            </svg>
        </div>

        <!-- 第12页：总结 -->
        <div class="slide">
            <svg viewBox="0 0 1920 1080">
                <rect width="100%" height="100%" fill="url(#bg1)"/>

                <text x="960" y="150" class="slide-title animate-text">Apache Spark 总结</text>

                <rect x="200" y="250" width="1520" height="650" fill="rgba(255,255,255,0.9)" rx="20" class="animate-text animate-delay-1"/>

                <text x="960" y="350" class="slide-text-center animate-text animate-delay-2" font-size="32px" class="accent">
                    统一的大数据处理平台
                </text>

                <rect x="300" y="420" width="1320" height="300" fill="rgba(52, 152, 219, 0.05)" rx="15" class="animate-text animate-delay-3"/>

                <text x="960" y="480" class="slide-text-center animate-text animate-delay-4" font-size="28px">
                    <tspan class="highlight">核心价值：</tspan>
                </text>

                <text x="960" y="530" class="slide-text-center animate-text animate-delay-5" font-size="24px">
                    ✨ 高性能的内存计算引擎
                </text>

                <text x="960" y="580" class="slide-text-center animate-text animate-delay-6" font-size="24px">
                    🔧 统一的编程模型和API
                </text>

                <text x="960" y="630" class="slide-text-center animate-text animate-delay-7" font-size="24px">
                    🚀 支持多种数据处理场景
                </text>

                <text x="960" y="680" class="slide-text-center animate-text animate-delay-8" font-size="24px">
                    🛡️ 强大的容错和扩展能力
                </text>

                <circle cx="960" cy="800" r="60" fill="#e74c3c" opacity="0.8" class="animate-text animate-delay-9">
                    <animate attributeName="r" values="60;70;60" dur="3s" repeatCount="indefinite"/>
                </circle>
                <text x="960" y="810" class="slide-text-center animate-text animate-delay-9" fill="white" font-weight="bold" font-size="20px">谢谢！</text>

                <text x="960" y="880" class="slide-text-center animate-text animate-delay-10" font-size="18px">
                    Apache Spark - 让大数据处理更简单、更快速
                </text>
            </svg>
        </div>

        <div class="controls">
            <button class="btn" onclick="previousSlide()">← 上一页</button>
            <button class="btn" onclick="nextSlide()">下一页 →</button>
            <button class="btn" onclick="toggleAutoPlay()">自动播放</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        let totalSlides = 12;
        let autoPlay = false;
        let autoPlayInterval;

        function showSlide(n) {
            const slides = document.querySelectorAll('.slide');

            if (n >= totalSlides) currentSlide = 0;
            if (n < 0) currentSlide = totalSlides - 1;

            slides.forEach(slide => slide.classList.remove('active'));
            slides[currentSlide].classList.add('active');

            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            currentSlide++;
            showSlide(currentSlide);
        }

        function previousSlide() {
            currentSlide--;
            showSlide(currentSlide);
        }

        function toggleAutoPlay() {
            const btn = event.target;
            if (autoPlay) {
                clearInterval(autoPlayInterval);
                autoPlay = false;
                btn.textContent = '自动播放';
                btn.style.background = 'rgba(255, 255, 255, 0.9)';
            } else {
                autoPlayInterval = setInterval(nextSlide, 5000);
                autoPlay = true;
                btn.textContent = '停止播放';
                btn.style.background = 'rgba(231, 76, 60, 0.9)';
                btn.style.color = 'white';
            }
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            } else if (e.key === 'Escape') {
                if (autoPlay) toggleAutoPlay();
            }
        });

        // 初始化
        document.getElementById('total-slides').textContent = totalSlides;
        showSlide(0);

        // 鼠标滚轮控制
        document.addEventListener('wheel', function(e) {
            if (e.deltaY > 0) {
                nextSlide();
            } else {
                previousSlide();
            }
        });

        // 触摸控制（移动设备）
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            if (touchEndX < touchStartX - 50) {
                nextSlide();
            }
            if (touchEndX > touchStartX + 50) {
                previousSlide();
            }
        }
    </script>
</body>
</html>
